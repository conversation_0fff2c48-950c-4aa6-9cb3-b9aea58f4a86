const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const app = express();

// 配置
const PORT = 3001;
const UPLOAD_DIR = path.join(__dirname, 'uploads');

// 确保上传目录存在
fs.ensureDirSync(UPLOAD_DIR);
fs.ensureDirSync(path.join(UPLOAD_DIR, 'documents'));
fs.ensureDirSync(path.join(UPLOAD_DIR, 'installers'));
fs.ensureDirSync(path.join(UPLOAD_DIR, 'others'));

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('uploads'));

// 配置文件类型分类
const DOCUMENT_TYPES = ['.txt', '.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx'];
const INSTALLER_TYPES = ['.exe', '.msi', '.dmg', '.pkg', '.deb', '.rpm', '.zip', '.rar'];

// 获取文件类型
function getFileType(filename) {
    const ext = path.extname(filename).toLowerCase();
    if (DOCUMENT_TYPES.includes(ext)) {
        return 'documents';
    } else if (INSTALLER_TYPES.includes(ext)) {
        return 'installers';
    } else {
        return 'others';
    }
}

// 配置multer用于文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const fileType = getFileType(file.originalname);
        cb(null, path.join(UPLOAD_DIR, fileType));
    },
    filename: function (req, file, cb) {
        // 保持原始文件名不变
        cb(null, file.originalname);
    }
});

const upload = multer({ storage: storage });

// 获取文件列表
app.get('/api/files', async (req, res) => {
    try {
        const files = {
            documents: [],
            installers: [],
            others: []
        };

        for (const category of ['documents', 'installers', 'others']) {
            const categoryPath = path.join(UPLOAD_DIR, category);
            if (await fs.pathExists(categoryPath)) {
                const items = await fs.readdir(categoryPath);
                for (const item of items) {
                    const filePath = path.join(categoryPath, item);
                    const stats = await fs.stat(filePath);
                    if (stats.isFile()) {
                        files[category].push({
                            name: item,
                            originalName: item,
                            size: stats.size,
                            uploadTime: stats.birthtime,
                            path: `/uploads/${category}/${item}`
                        });
                    }
                }
            }
        }

        res.json({
            success: true,
            data: files
        });
    } catch (error) {
        console.error('获取文件列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取文件列表失败'
        });
    }
});

// 文件上传
app.post('/api/upload', upload.single('file'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '没有选择文件'
            });
        }

        res.json({
            success: true,
            message: '文件上传成功',
            data: {
                filename: req.file.filename,
                originalName: req.file.originalname,
                size: req.file.size,
                path: req.file.path
            }
        });
    } catch (error) {
        console.error('文件上传失败:', error);
        res.status(500).json({
            success: false,
            message: '文件上传失败'
        });
    }
});

// 文件下载
app.get('/api/download/:category/:filename', (req, res) => {
    try {
        const { category, filename } = req.params;
        const filePath = path.join(UPLOAD_DIR, category, filename);

        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        res.download(filePath, filename);
    } catch (error) {
        console.error('文件下载失败:', error);
        res.status(500).json({
            success: false,
            message: '文件下载失败'
        });
    }
});

// 删除文件
app.delete('/api/files/:category/:filename', async (req, res) => {
    try {
        const { category, filename } = req.params;
        console.log(`删除请求: category=${category}, filename=${filename}`);
        
        const filePath = path.join(UPLOAD_DIR, category, decodeURIComponent(filename));
        console.log(`文件路径: ${filePath}`);

        if (!await fs.pathExists(filePath)) {
            console.log('文件不存在');
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        console.log('开始删除文件...');
        await fs.remove(filePath);
        console.log('文件删除成功');
        
        res.json({
            success: true,
            message: '文件删除成功'
        });
    } catch (error) {
        console.error('文件删除失败:', error);
        res.status(500).json({
            success: false,
            message: `文件删除失败: ${error.message}`
        });
    }
});

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: '服务运行正常'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`云盘后端服务运行在 http://localhost:${PORT}`);
    console.log(`上传目录: ${UPLOAD_DIR}`);
});
