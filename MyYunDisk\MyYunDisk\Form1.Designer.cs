namespace MyYunDisk
{
    partial class Form1
    {
        private System.ComponentModel.IContainer components = null;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabDocuments;
        private System.Windows.Forms.TabPage tabInstallers;
        private System.Windows.Forms.TabPage tabOthers;
        private System.Windows.Forms.ListView listViewDocuments;
        private System.Windows.Forms.ListView listViewInstallers;
        private System.Windows.Forms.ListView listViewOthers;
        private System.Windows.Forms.Button btnUpload;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnSettings;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.ColumnHeader columnHeader6;
        private System.Windows.Forms.ColumnHeader columnHeader7;
        private System.Windows.Forms.ColumnHeader columnHeader8;
        private System.Windows.Forms.ColumnHeader columnHeader9;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabDocuments = new System.Windows.Forms.TabPage();
            this.listViewDocuments = new System.Windows.Forms.ListView();
            this.columnHeader1 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader2 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader3 = new System.Windows.Forms.ColumnHeader();
            this.tabInstallers = new System.Windows.Forms.TabPage();
            this.listViewInstallers = new System.Windows.Forms.ListView();
            this.columnHeader4 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader5 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader6 = new System.Windows.Forms.ColumnHeader();
            this.tabOthers = new System.Windows.Forms.TabPage();
            this.listViewOthers = new System.Windows.Forms.ListView();
            this.columnHeader7 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader8 = new System.Windows.Forms.ColumnHeader();
            this.columnHeader9 = new System.Windows.Forms.ColumnHeader();
            this.btnUpload = new System.Windows.Forms.Button();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.btnSettings = new System.Windows.Forms.Button();
            this.btnDelete = new System.Windows.Forms.Button();
            this.tabControl1.SuspendLayout();
            this.tabDocuments.SuspendLayout();
            this.tabInstallers.SuspendLayout();
            this.tabOthers.SuspendLayout();
            this.SuspendLayout();
            
            // tabControl1
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Controls.Add(this.tabDocuments);
            this.tabControl1.Controls.Add(this.tabInstallers);
            this.tabControl1.Controls.Add(this.tabOthers);
            this.tabControl1.Location = new System.Drawing.Point(12, 12);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(760, 487);
            this.tabControl1.TabIndex = 0;
            
            // tabDocuments
            this.tabDocuments.Controls.Add(this.listViewDocuments);
            this.tabDocuments.Location = new System.Drawing.Point(4, 22);
            this.tabDocuments.Name = "tabDocuments";
            this.tabDocuments.Padding = new System.Windows.Forms.Padding(3);
            this.tabDocuments.Size = new System.Drawing.Size(752, 461);
            this.tabDocuments.TabIndex = 0;
            this.tabDocuments.Text = "文档";
            this.tabDocuments.UseVisualStyleBackColor = true;
            
            // listViewDocuments
            this.listViewDocuments.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3});
            this.listViewDocuments.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewDocuments.FullRowSelect = true;
            this.listViewDocuments.GridLines = true;
            this.listViewDocuments.HideSelection = false;
            this.listViewDocuments.Location = new System.Drawing.Point(3, 3);
            this.listViewDocuments.Name = "listViewDocuments";
            this.listViewDocuments.Size = new System.Drawing.Size(746, 455);
            this.listViewDocuments.TabIndex = 0;
            this.listViewDocuments.UseCompatibleStateImageBehavior = false;
            this.listViewDocuments.View = System.Windows.Forms.View.Details;
            this.listViewDocuments.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewDocuments_MouseDoubleClick);
            
            // columnHeader1
            this.columnHeader1.Text = "文件名";
            this.columnHeader1.Width = 300;
            
            // columnHeader2
            this.columnHeader2.Text = "大小";
            this.columnHeader2.Width = 100;
            
            // columnHeader3
            this.columnHeader3.Text = "上传时间";
            this.columnHeader3.Width = 150;
            
            // tabInstallers
            this.tabInstallers.Controls.Add(this.listViewInstallers);
            this.tabInstallers.Location = new System.Drawing.Point(4, 22);
            this.tabInstallers.Name = "tabInstallers";
            this.tabInstallers.Padding = new System.Windows.Forms.Padding(3);
            this.tabInstallers.Size = new System.Drawing.Size(752, 461);
            this.tabInstallers.TabIndex = 1;
            this.tabInstallers.Text = "安装文件";
            this.tabInstallers.UseVisualStyleBackColor = true;
            
            // listViewInstallers
            this.listViewInstallers.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader6});
            this.listViewInstallers.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewInstallers.FullRowSelect = true;
            this.listViewInstallers.GridLines = true;
            this.listViewInstallers.HideSelection = false;
            this.listViewInstallers.Location = new System.Drawing.Point(3, 3);
            this.listViewInstallers.Name = "listViewInstallers";
            this.listViewInstallers.Size = new System.Drawing.Size(746, 455);
            this.listViewInstallers.TabIndex = 0;
            this.listViewInstallers.UseCompatibleStateImageBehavior = false;
            this.listViewInstallers.View = System.Windows.Forms.View.Details;
            this.listViewInstallers.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewInstallers_MouseDoubleClick);
            
            // columnHeader4
            this.columnHeader4.Text = "文件名";
            this.columnHeader4.Width = 300;
            
            // columnHeader5
            this.columnHeader5.Text = "大小";
            this.columnHeader5.Width = 100;
            
            // columnHeader6
            this.columnHeader6.Text = "上传时间";
            this.columnHeader6.Width = 150;
            
            // tabOthers
            this.tabOthers.Controls.Add(this.listViewOthers);
            this.tabOthers.Location = new System.Drawing.Point(4, 22);
            this.tabOthers.Name = "tabOthers";
            this.tabOthers.Padding = new System.Windows.Forms.Padding(3);
            this.tabOthers.Size = new System.Drawing.Size(752, 461);
            this.tabOthers.TabIndex = 2;
            this.tabOthers.Text = "其他文件";
            this.tabOthers.UseVisualStyleBackColor = true;
            
            // listViewOthers
            this.listViewOthers.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader7,
            this.columnHeader8,
            this.columnHeader9});
            this.listViewOthers.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewOthers.FullRowSelect = true;
            this.listViewOthers.GridLines = true;
            this.listViewOthers.HideSelection = false;
            this.listViewOthers.Location = new System.Drawing.Point(3, 3);
            this.listViewOthers.Name = "listViewOthers";
            this.listViewOthers.Size = new System.Drawing.Size(746, 455);
            this.listViewOthers.TabIndex = 0;
            this.listViewOthers.UseCompatibleStateImageBehavior = false;
            this.listViewOthers.View = System.Windows.Forms.View.Details;
            this.listViewOthers.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewOthers_MouseDoubleClick);
            
            // columnHeader7
            this.columnHeader7.Text = "文件名";
            this.columnHeader7.Width = 300;
            
            // columnHeader8
            this.columnHeader8.Text = "大小";
            this.columnHeader8.Width = 100;
            
            // columnHeader9
            this.columnHeader9.Text = "上传时间";
            this.columnHeader9.Width = 150;
            
            // btnUpload
            this.btnUpload.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnUpload.Location = new System.Drawing.Point(12, 505);
            this.btnUpload.Name = "btnUpload";
            this.btnUpload.Size = new System.Drawing.Size(75, 23);
            this.btnUpload.TabIndex = 1;
            this.btnUpload.Text = "上传文件";
            this.btnUpload.UseVisualStyleBackColor = true;
            this.btnUpload.Click += new System.EventHandler(this.btnUpload_Click);
            
            // btnDelete
            this.btnDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnDelete.Location = new System.Drawing.Point(93, 505);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(75, 23);
            this.btnDelete.TabIndex = 2;
            this.btnDelete.Text = "删除";
            this.btnDelete.UseVisualStyleBackColor = true;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            
            // btnRefresh
            this.btnRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRefresh.Location = new System.Drawing.Point(174, 505);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(75, 23);
            this.btnRefresh.TabIndex = 3;
            this.btnRefresh.Text = "刷新";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            
            // btnSettings
            this.btnSettings.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnSettings.Location = new System.Drawing.Point(255, 505);
            this.btnSettings.Name = "btnSettings";
            this.btnSettings.Size = new System.Drawing.Size(75, 23);
            this.btnSettings.TabIndex = 4;
            this.btnSettings.Text = "设置";
            this.btnSettings.UseVisualStyleBackColor = true;
            this.btnSettings.Click += new System.EventHandler(this.btnSettings_Click);
            
            // Form1
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 540);
            // progressBar1
            this.progressBar1 = new System.Windows.Forms.ProgressBar();
            this.progressBar1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.progressBar1.Location = new System.Drawing.Point(336, 505);
            this.progressBar1.Name = "progressBar1";
            this.progressBar1.Size = new System.Drawing.Size(436, 23);
            this.progressBar1.TabIndex = 5;
            this.progressBar1.Visible = false;
            
            this.Controls.Add(this.progressBar1);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnSettings);
            this.Controls.Add(this.btnRefresh);
            this.Controls.Add(this.btnUpload);
            this.Controls.Add(this.tabControl1);
            this.MinimumSize = new System.Drawing.Size(800, 579);
            this.Name = "Form1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "我的云盘";
            this.tabControl1.ResumeLayout(false);
            this.tabDocuments.ResumeLayout(false);
            this.tabInstallers.ResumeLayout(false);
            this.tabOthers.ResumeLayout(false);
            this.ResumeLayout(false);
        }
    }
}
