using System;
using System.Windows.Forms;
using System.IO;

namespace MyYunDisk
{
    public partial class SettingsForm : Form
    {
        private Label lblStoragePath;
        private TextBox txtStoragePath;
        private Button btnBrowse;
        private Button btnSave;
        private Button btnCancel;
        
        public string StoragePath { get; private set; }

        public SettingsForm(string currentPath)
        {
            InitializeComponent();
            // 从设置加载存储路径，如果不存在则使用传入的默认路径
            StoragePath = Properties.Settings.Default.StoragePath ?? currentPath;
            txtStoragePath.Text = StoragePath;
        }

        private void btnBrowse_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.SelectedPath = StoragePath;
                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    txtStoragePath.Text = folderDialog.SelectedPath;
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (Directory.Exists(txtStoragePath.Text))
            {
                StoragePath = txtStoragePath.Text;
                // 保存设置到配置文件
                Properties.Settings.Default.StoragePath = StoragePath;
                Properties.Settings.Default.Save();
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                MessageBox.Show("指定的目录不存在，请重新选择", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            }
    }
}
