using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace MyYunDisk
{
    public partial class Form1 : Form
    {
        private readonly HttpClient httpClient;
        private readonly string baseUrl = "http://115.159.107.121:3001/api";
        private string storagePath = Path.GetDirectoryName(Application.ExecutablePath);
        private System.Windows.Forms.ProgressBar progressBar1;
        
        public Form1()
        {
            InitializeComponent();
            httpClient = new HttpClient();
            LoadFiles();
        }

        private async void LoadFiles()
        {
            try
            {
                var response = await httpClient.GetAsync($"{baseUrl}/files");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<ApiResponse<FilesData>>(json);
                    
                    if (result?.success == true && result.data != null)
                    {
                        DisplayFiles(result.data);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DisplayFiles(FilesData files)
        {
            listViewDocuments.Items.Clear();
            listViewInstallers.Items.Clear();
            listViewOthers.Items.Clear();

            foreach (var file in files.documents)
            {
                var item = new ListViewItem(file.originalName);
                item.SubItems.Add(FormatFileSize(file.size));
                item.SubItems.Add(file.uploadTime.ToString("yyyy-MM-dd HH:mm:ss"));
                item.Tag = new FileInfo("documents", file.originalName);
                listViewDocuments.Items.Add(item);
            }

            foreach (var file in files.installers)
            {
                var item = new ListViewItem(file.originalName);
                item.SubItems.Add(FormatFileSize(file.size));
                item.SubItems.Add(file.uploadTime.ToString("yyyy-MM-dd HH:mm:ss"));
                item.Tag = new FileInfo("installers", file.originalName);
                listViewInstallers.Items.Add(item);
            }

            foreach (var file in files.others)
            {
                var item = new ListViewItem(file.originalName);
                item.SubItems.Add(FormatFileSize(file.size));
                item.SubItems.Add(file.uploadTime.ToString("yyyy-MM-dd HH:mm:ss"));
                item.Tag = new FileInfo("others", file.originalName);
                listViewOthers.Items.Add(item);
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private async void btnUpload_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Multiselect = true;
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    progressBar1.Visible = true;
                    progressBar1.Value = 0;

                    int successCount = 0;
                    int failCount = 0;

                    foreach (var filePath in openFileDialog.FileNames)
                    {
                        try
                        {
                            var fileExt = Path.GetExtension(filePath).ToLower();
                            var fileInfo = new System.IO.FileInfo(filePath);
                            var fileSize = fileInfo.Length;

                            string category = "others";
                            if (new[] { ".txt", ".doc", ".docx", ".pdf", ".xls", ".xlsx", ".ppt", ".pptx" }.Contains(fileExt))
                            {
                                category = "documents";
                            }
                            else if (new[] { ".exe", ".msi", ".dmg", ".pkg", ".deb", ".rpm" }.Contains(fileExt))
                            {
                                category = "installers";
                            }

                            using (var fileStream = File.OpenRead(filePath))
                            using (var progressStream = new ProgressStream(fileStream))
                            using (var form = new MultipartFormDataContent())
                            {
                                progressStream.ProgressChanged += (bytesRead, totalBytes) =>
                                {
                                    var progress = (int)((double)bytesRead / totalBytes * 100);
                                    progressBar1.Invoke((MethodInvoker)(() => progressBar1.Value = progress));
                                };

                                var fileContent = new StreamContent(progressStream);
                                form.Add(fileContent, "file", Path.GetFileName(filePath));
                                form.Add(new StringContent(category), "category");

                                var response = await httpClient.PostAsync($"{baseUrl}/upload", form);
                                var json = await response.Content.ReadAsStringAsync();
                                var result = JsonConvert.DeserializeObject<ApiResponse<object>>(json);

                                if (result?.success == true)
                                {
                                    successCount++;
                                }
                                else
                                {
                                    failCount++;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            failCount++;
                            MessageBox.Show($"上传文件 {Path.GetFileName(filePath)} 失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }

                    // 确保进度条显示100%然后隐藏，不阻塞主流程
                    progressBar1.Invoke((MethodInvoker)(() =>
                    {
                        progressBar1.Value = 100;
                        // 使用Timer异步隐藏进度条，不阻塞主流程
                        var timer = new System.Windows.Forms.Timer();
                        timer.Interval = 300; // 300ms后隐藏
                        timer.Tick += (s, args) =>
                        {
                            timer.Stop();
                            timer.Dispose();
                            progressBar1.Visible = false;
                        };
                        timer.Start();
                    }));
                    
                    // 显示上传结果
                    string message = successCount > 0 
                        ? $"上传成功 {successCount} 个文件" + (failCount > 0 ? $"，失败 {failCount} 个" : "")
                        : "上传失败";
                    MessageBox.Show(message, "上传结果", MessageBoxButtons.OK, 
                        successCount > 0 ? MessageBoxIcon.Information : MessageBoxIcon.Error);
                    LoadFiles();
                }
            }
        }

        private async void listViewDocuments_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            await DownloadSelectedFile(listViewDocuments);
        }

        private async void listViewInstallers_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            await DownloadSelectedFile(listViewInstallers);
        }

        private async void listViewOthers_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            await DownloadSelectedFile(listViewOthers);
        }

        private async Task DownloadSelectedFile(ListView listView)
        {
            if (listView.SelectedItems.Count > 0)
            {
                var selectedItem = listView.SelectedItems[0];
                var fileInfo = (FileInfo)selectedItem.Tag;
                var localPath = Path.Combine(storagePath, fileInfo.FileName);

                if (File.Exists(localPath))
                {
                    try 
                    {
                        if (fileInfo.Category == "installers")
                        {
                            var result = MessageBox.Show("是否要安装此程序？", "安装确认", 
                                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                            
                            if (result == DialogResult.Yes)
                            {
                                System.Diagnostics.Process.Start(localPath);
                            }
                        }
                        else
                        {
                            System.Diagnostics.Process.Start(localPath);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"打开文件失败: {ex.Message}", "错误", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    using (var saveFileDialog = new SaveFileDialog())
                    {
                        saveFileDialog.FileName = fileInfo.FileName;
                        saveFileDialog.InitialDirectory = storagePath;
                        if (saveFileDialog.ShowDialog() == DialogResult.OK)
                        {
                            try
                            {
                                progressBar1.Visible = true;
                                progressBar1.Value = 0;

                                var response = await httpClient.GetAsync($"{baseUrl}/download/{fileInfo.Category}/{fileInfo.FileName}", 
                                    HttpCompletionOption.ResponseHeadersRead);
                                
                                if (response.IsSuccessStatusCode)
                                {
                                    var totalBytes = response.Content.Headers.ContentLength ?? 0;
                                    var fileStream = File.Create(saveFileDialog.FileName);
                                    
                                    using (var stream = await response.Content.ReadAsStreamAsync())
                                    using (var progressStream = new ProgressStream(stream))
                                    {
                                        progressStream.ProgressChanged += (bytesRead, total) => 
                                        {
                                            var progress = (int)((double)bytesRead / total * 100);
                                            progressBar1.Invoke((MethodInvoker)(() => progressBar1.Value = progress));
                                        };
                                        
                                        await progressStream.CopyToAsync(fileStream);
                                    }

                                    // 确保进度条显示100%并保持一段时间
                                    progressBar1.Invoke((MethodInvoker)(() => progressBar1.Value = 100));
                                    await Task.Delay(500); // 保持500ms让用户看到完成状态
                                    MessageBox.Show("文件下载成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                                else
                                {
                                    MessageBox.Show("文件下载失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"下载失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                            finally
                            {
                                progressBar1.Invoke((MethodInvoker)(() => progressBar1.Visible = false));
                            }
                        }
                    }
                }
            }
        }

        private class ProgressStream : Stream
        {
            private readonly Stream _innerStream;
            private long _bytesRead;
            private readonly long _totalBytes;
            private DateTime _lastUpdateTime = DateTime.MinValue;

            public event Action<long, long> ProgressChanged;

            public ProgressStream(Stream innerStream)
            {
                _innerStream = innerStream;
                _totalBytes = innerStream.Length;
            }

            public override bool CanRead => _innerStream.CanRead;
            public override bool CanSeek => _innerStream.CanSeek;
            public override bool CanWrite => _innerStream.CanWrite;
            public override long Length => _innerStream.Length;
            public override long Position
            {
                get => _innerStream.Position;
                set => _innerStream.Position = value;
            }

            public override void Flush() => _innerStream.Flush();
            public override long Seek(long offset, SeekOrigin origin) => _innerStream.Seek(offset, origin);
            public override void SetLength(long value) => _innerStream.SetLength(value);
            public override void Write(byte[] buffer, int offset, int count) => _innerStream.Write(buffer, offset, count);

            public override int Read(byte[] buffer, int offset, int count)
            {
                var bytesRead = _innerStream.Read(buffer, offset, count);
                _bytesRead += bytesRead;
                
                // 限制更新频率，每100ms更新一次
                if ((DateTime.Now - _lastUpdateTime).TotalMilliseconds > 100 || _bytesRead == _totalBytes)
                {
                    ProgressChanged?.Invoke(_bytesRead, _totalBytes);
                    _lastUpdateTime = DateTime.Now;
                    Application.DoEvents(); // 处理UI消息队列
                }
                return bytesRead;
            }

            public override async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
            {
                var bytesRead = await _innerStream.ReadAsync(buffer, offset, count, cancellationToken);
                _bytesRead += bytesRead;

                // 限制更新频率，每100ms更新一次或读取完成时强制更新
                if ((DateTime.Now - _lastUpdateTime).TotalMilliseconds > 100 || _bytesRead == _totalBytes)
                {
                    ProgressChanged?.Invoke(_bytesRead, _totalBytes);
                    _lastUpdateTime = DateTime.Now;
                }

                return bytesRead;
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadFiles();
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            using (var settingsForm = new SettingsForm(storagePath))
            {
                if (settingsForm.ShowDialog() == DialogResult.OK)
                {
                    storagePath = settingsForm.StoragePath;
                }
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            ListView currentListView = null;
            switch (tabControl1.SelectedIndex)
            {
                case 0: currentListView = listViewDocuments; break;
                case 1: currentListView = listViewInstallers; break;
                case 2: currentListView = listViewOthers; break;
            }

            if (currentListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要删除的文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var fileInfo = (FileInfo)currentListView.SelectedItems[0].Tag;
            var result = MessageBox.Show($"确定要删除文件 {fileInfo.FileName} 吗？", "确认删除", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var response = await httpClient.DeleteAsync($"{baseUrl}/files/{fileInfo.Category}/{fileInfo.FileName}");
                    if (response.IsSuccessStatusCode)
                    {
                        currentListView.SelectedItems[0].Remove();
                        
                        var localPath = Path.Combine(storagePath, fileInfo.FileName);
                        if (File.Exists(localPath))
                        {
                            File.Delete(localPath);
                        }
                        
                        MessageBox.Show("文件删除成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("文件删除失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"删除文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }

    public class ApiResponse<T>
    {
        public bool success { get; set; }
        public string message { get; set; }
        public T data { get; set; }
    }

    public class FilesData
    {
        public List<FileItem> documents { get; set; }
        public List<FileItem> installers { get; set; }
        public List<FileItem> others { get; set; }
    }

    public class FileItem
    {
        public string name { get; set; }
        public string originalName { get; set; }
        public long size { get; set; }
        public DateTime uploadTime { get; set; }
        public string path { get; set; }
    }

    public class FileInfo
    {
        public string Category { get; }
        public string FileName { get; }

        public FileInfo(string category, string fileName)
        {
            Category = category ?? throw new ArgumentNullException(nameof(category));
            FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        }
    }
}
